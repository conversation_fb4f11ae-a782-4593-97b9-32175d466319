{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from './shared/src/utils.js';\nimport Icon from './Icon.js';\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef(({\n    className,\n    ...props\n  }, ref) => createElement(Icon, {\n    ref,\n    iconNode,\n    className: mergeClasses(`lucide-${toKebabCase(toPascalCase(iconName))}`, `lucide-${iconName}`, className),\n    ...props\n  }));\n  Component.displayName = toPascalCase(iconName);\n  return Component;\n};\nexport { createLucideIcon as default };", "map": {"version": 3, "names": ["createLucideIcon", "iconName", "iconNode", "Component", "forwardRef", "className", "props", "ref", "createElement", "Icon", "mergeClasses", "toKebabCase", "toPascalCase", "displayName"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "mappings": ";;;;;;;;;;AAWA,MAAMA,gBAAA,GAAmBA,CAACC,QAAA,EAAkBC,QAAA,KAAuB;EACjE,MAAMC,SAAA,GAAYC,UAAA,CAAuC,CAAC;IAAEC,SAAA;IAAW,GAAGC;EAAA,CAAM,EAAGC,GAAA,KACjFC,aAAA,CAAcC,IAAA,EAAM;IAClBF,GAAA;IACAL,QAAA;IACAG,SAAA,EAAWK,YAAA,CACT,UAAUC,WAAA,CAAYC,YAAA,CAAaX,QAAQ,CAAC,CAAC,IAC7C,UAAUA,QAAQ,IAClBI,SAAA,CACF;IACA,GAAGC;EAAA,CACJ,EACH;EAEAH,SAAA,CAAUU,WAAA,GAAcD,YAAA,CAAaX,QAAQ;EAE7C,OAAOE,SAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}