{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"6\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1lh9wr\"\n}], [\"path\", {\n  d: \"M8.12 8.12 12 12\",\n  key: \"1alkpv\"\n}], [\"path\", {\n  d: \"M20 4 8.12 15.88\",\n  key: \"xgtan2\"\n}], [\"circle\", {\n  cx: \"6\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"fqmcym\"\n}], [\"path\", {\n  d: \"M14.8 14.8 20 20\",\n  key: \"ptml3r\"\n}]];\nconst Scissors = createLucideIcon(\"scissors\", __iconNode);\nexport { __iconNode, Scissors as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "Scissors", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\scissors.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '6', cy: '6', r: '3', key: '1lh9wr' }],\n  ['path', { d: 'M8.12 8.12 12 12', key: '1alkpv' }],\n  ['path', { d: 'M20 4 8.12 15.88', key: 'xgtan2' }],\n  ['circle', { cx: '6', cy: '18', r: '3', key: 'fqmcym' }],\n  ['path', { d: 'M14.8 14.8 20 20', key: 'ptml3r' }],\n];\n\n/**\n * @component @name Scissors\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI2IiBjeT0iNiIgcj0iMyIgLz4KICA8cGF0aCBkPSJNOC4xMiA4LjEyIDEyIDEyIiAvPgogIDxwYXRoIGQ9Ik0yMCA0IDguMTIgMTUuODgiIC8+CiAgPGNpcmNsZSBjeD0iNiIgY3k9IjE4IiByPSIzIiAvPgogIDxwYXRoIGQ9Ik0xNC44IDE0LjggMjAgMjAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/scissors\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Scissors = createLucideIcon('scissors', __iconNode);\n\nexport default Scissors;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,UAAU;EAAEC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAKC,CAAA,EAAG;EAAKC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAoBD,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAoBD,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,UAAU;EAAEH,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAMC,CAAA,EAAG;EAAKC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAoBD,GAAA,EAAK;AAAA,CAAU,EACnD;AAaA,MAAME,QAAA,GAAWC,gBAAA,CAAiB,YAAYP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}