{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15.033 9.44a.647.647 0 0 1 0 1.12l-4.065 2.352a.645.645 0 0 1-.968-.56V7.648a.645.645 0 0 1 .967-.56z\",\n  key: \"vbtd3f\"\n}], [\"path\", {\n  d: \"M12 17v4\",\n  key: \"1riwvh\"\n}], [\"path\", {\n  d: \"M8 21h8\",\n  key: \"1ev6f3\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"3\",\n  width: \"20\",\n  height: \"14\",\n  rx: \"2\",\n  key: \"x3v2xh\"\n}]];\nconst MonitorPlay = createLucideIcon(\"monitor-play\", __iconNode);\nexport { __iconNode, MonitorPlay as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x", "y", "width", "height", "rx", "MonitorPlay", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\monitor-play.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M15.033 9.44a.647.647 0 0 1 0 1.12l-4.065 2.352a.645.645 0 0 1-.968-.56V7.648a.645.645 0 0 1 .967-.56z',\n      key: 'vbtd3f',\n    },\n  ],\n  ['path', { d: 'M12 17v4', key: '1riwvh' }],\n  ['path', { d: 'M8 21h8', key: '1ev6f3' }],\n  ['rect', { x: '2', y: '3', width: '20', height: '14', rx: '2', key: 'x3v2xh' }],\n];\n\n/**\n * @component @name MonitorPlay\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUuMDMzIDkuNDRhLjY0Ny42NDcgMCAwIDEgMCAxLjEybC00LjA2NSAyLjM1MmEuNjQ1LjY0NSAwIDAgMS0uOTY4LS41NlY3LjY0OGEuNjQ1LjY0NSAwIDAgMSAuOTY3LS41NnoiIC8+CiAgPHBhdGggZD0iTTEyIDE3djQiIC8+CiAgPHBhdGggZD0iTTggMjFoOCIgLz4KICA8cmVjdCB4PSIyIiB5PSIzIiB3aWR0aD0iMjAiIGhlaWdodD0iMTQiIHJ4PSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/monitor-play\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MonitorPlay = createLucideIcon('monitor-play', __iconNode);\n\nexport default MonitorPlay;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CACE,QACA;EACEC,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EAET,EACA,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAKC,CAAA,EAAG;EAAKC,KAAA,EAAO;EAAMC,MAAA,EAAQ;EAAMC,EAAA,EAAI;EAAKL,GAAA,EAAK;AAAA,CAAU,EAChF;AAaA,MAAMM,WAAA,GAAcC,gBAAA,CAAiB,gBAAgBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}