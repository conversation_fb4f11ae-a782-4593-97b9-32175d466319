{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 8c2-3-2-3 0-6\",\n  key: \"1ldv5m\"\n}], [\"path\", {\n  d: \"M15.5 8c2-3-2-3 0-6\",\n  key: \"1otqoz\"\n}], [\"path\", {\n  d: \"M6 10h.01\",\n  key: \"1lbq93\"\n}], [\"path\", {\n  d: \"M6 14h.01\",\n  key: \"zudwn7\"\n}], [\"path\", {\n  d: \"M10 16v-4\",\n  key: \"1c25yv\"\n}], [\"path\", {\n  d: \"M14 16v-4\",\n  key: \"1dkbt8\"\n}], [\"path\", {\n  d: \"M18 16v-4\",\n  key: \"1yg9me\"\n}], [\"path\", {\n  d: \"M20 6a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h3\",\n  key: \"1ubg90\"\n}], [\"path\", {\n  d: \"M5 20v2\",\n  key: \"1abpe8\"\n}], [\"path\", {\n  d: \"M19 20v2\",\n  key: \"kqn6ft\"\n}]];\nconst Heater = createLucideIcon(\"heater\", __iconNode);\nexport { __iconNode, Heater as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Heater", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\heater.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M11 8c2-3-2-3 0-6', key: '1ldv5m' }],\n  ['path', { d: 'M15.5 8c2-3-2-3 0-6', key: '1otqoz' }],\n  ['path', { d: 'M6 10h.01', key: '1lbq93' }],\n  ['path', { d: 'M6 14h.01', key: 'zudwn7' }],\n  ['path', { d: 'M10 16v-4', key: '1c25yv' }],\n  ['path', { d: 'M14 16v-4', key: '1dkbt8' }],\n  ['path', { d: 'M18 16v-4', key: '1yg9me' }],\n  [\n    'path',\n    { d: 'M20 6a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h3', key: '1ubg90' },\n  ],\n  ['path', { d: 'M5 20v2', key: '1abpe8' }],\n  ['path', { d: 'M19 20v2', key: 'kqn6ft' }],\n];\n\n/**\n * @component @name Heater\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEgOGMyLTMtMi0zIDAtNiIgLz4KICA8cGF0aCBkPSJNMTUuNSA4YzItMy0yLTMgMC02IiAvPgogIDxwYXRoIGQ9Ik02IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNNiAxNGguMDEiIC8+CiAgPHBhdGggZD0iTTEwIDE2di00IiAvPgogIDxwYXRoIGQ9Ik0xNCAxNnYtNCIgLz4KICA8cGF0aCBkPSJNMTggMTZ2LTQiIC8+CiAgPHBhdGggZD0iTTIwIDZhMiAyIDAgMCAxIDIgMnYxMGEyIDIgMCAwIDEtMiAySDRhMiAyIDAgMCAxLTItMlY4YTIgMiAwIDAgMSAyLTJoMyIgLz4KICA8cGF0aCBkPSJNNSAyMHYyIiAvPgogIDxwYXRoIGQ9Ik0xOSAyMHYyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/heater\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Heater = createLucideIcon('heater', __iconNode);\n\nexport default Heater;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAqBC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAuBC,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CACE,QACA;EAAED,CAAA,EAAG;EAA0EC,GAAA,EAAK;AAAA,EACtF,EACA,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,EAC3C;AAaA,MAAMC,MAAA,GAASC,gBAAA,CAAiB,UAAUJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}