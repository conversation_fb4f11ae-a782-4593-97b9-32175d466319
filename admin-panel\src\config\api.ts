// API Configuration - Easy to replace with your own API
export const API_CONFIG = {
  // Base URL for your API - Change this to your actual API endpoint
  BASE_URL: process.env.REACT_APP_API_URL || 'https://jsonplaceholder.typicode.com',
  
  // Authentication endpoints
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    ME: '/auth/me',
  },
  
  // User management endpoints
  USERS: {
    LIST: '/users',
    CREATE: '/users',
    UPDATE: '/users',
    DELETE: '/users',
    PROFILE: '/users/profile',
  },
  
  // Dashboard endpoints
  DASHBOARD: {
    STATS: '/dashboard/stats',
    RECENT_ACTIVITY: '/dashboard/activity',
    CHARTS: '/dashboard/charts',
  },
  
  // Other endpoints can be added here
  POSTS: '/posts',
  COMMENTS: '/comments',
};

// Request timeout
export const REQUEST_TIMEOUT = 10000;

// Token storage keys
export const STORAGE_KEYS = {
  TOKEN: 'admin_token',
  REFRESH_TOKEN: 'admin_refresh_token',
  USER: 'admin_user',
};

// Mock API responses for demo purposes
// Replace these with actual API calls to your backend
export const MOCK_RESPONSES = {
  // Mock login response
  LOGIN_SUCCESS: {
    user: {
      id: '1',
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'admin',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
      permissions: ['read', 'write', 'delete', 'admin'],
    },
    token: 'mock-jwt-token-12345',
    refreshToken: 'mock-refresh-token-67890',
  },
  
  // Mock dashboard stats
  DASHBOARD_STATS: {
    totalUsers: 1234,
    totalPosts: 567,
    totalComments: 890,
    activeUsers: 123,
  },
  
  // Mock recent activity
  RECENT_ACTIVITY: [
    { id: 1, action: 'User registered', user: 'John Doe', timestamp: new Date().toISOString() },
    { id: 2, action: 'Post created', user: 'Jane Smith', timestamp: new Date().toISOString() },
    { id: 3, action: 'Comment added', user: 'Bob Johnson', timestamp: new Date().toISOString() },
  ],
};
