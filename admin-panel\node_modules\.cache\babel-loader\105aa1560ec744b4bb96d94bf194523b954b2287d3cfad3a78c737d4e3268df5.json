{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"8\",\n  x: \"2\",\n  y: \"2\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"ngkwjq\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"8\",\n  x: \"2\",\n  y: \"14\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"iecqi9\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"6.01\",\n  y1: \"6\",\n  y2: \"6\",\n  key: \"16zg32\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"6.01\",\n  y1: \"18\",\n  y2: \"18\",\n  key: \"nzw8ys\"\n}]];\nconst Server = createLucideIcon(\"server\", __iconNode);\nexport { __iconNode, Server as default };", "map": {"version": 3, "names": ["__iconNode", "width", "height", "x", "y", "rx", "ry", "key", "x1", "x2", "y1", "y2", "Server", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\server.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '20', height: '8', x: '2', y: '2', rx: '2', ry: '2', key: 'ngkwjq' }],\n  ['rect', { width: '20', height: '8', x: '2', y: '14', rx: '2', ry: '2', key: 'iecqi9' }],\n  ['line', { x1: '6', x2: '6.01', y1: '6', y2: '6', key: '16zg32' }],\n  ['line', { x1: '6', x2: '6.01', y1: '18', y2: '18', key: 'nzw8ys' }],\n];\n\n/**\n * @component @name Server\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iOCIgeD0iMiIgeT0iMiIgcng9IjIiIHJ5PSIyIiAvPgogIDxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSI4IiB4PSIyIiB5PSIxNCIgcng9IjIiIHJ5PSIyIiAvPgogIDxsaW5lIHgxPSI2IiB4Mj0iNi4wMSIgeTE9IjYiIHkyPSI2IiAvPgogIDxsaW5lIHgxPSI2IiB4Mj0iNi4wMSIgeTE9IjE4IiB5Mj0iMTgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/server\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Server = createLucideIcon('server', __iconNode);\n\nexport default Server;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAA,EAAQ;EAAKC,CAAA,EAAG;EAAKC,CAAA,EAAG;EAAKC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAKC,GAAA,EAAK;AAAA,CAAU,GACtF,CAAC,QAAQ;EAAEN,KAAA,EAAO;EAAMC,MAAA,EAAQ;EAAKC,CAAA,EAAG;EAAKC,CAAA,EAAG;EAAMC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAKC,GAAA,EAAK;AAAA,CAAU,GACvF,CAAC,QAAQ;EAAEC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAQC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAKJ,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAQC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMJ,GAAA,EAAK;AAAA,CAAU,EACrE;AAaA,MAAMK,MAAA,GAASC,gBAAA,CAAiB,UAAUb,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}