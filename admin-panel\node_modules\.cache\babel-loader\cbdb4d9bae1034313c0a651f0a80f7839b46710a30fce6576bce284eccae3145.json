{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10 20-1.25-2.5L6 18\",\n  key: \"18frcb\"\n}], [\"path\", {\n  d: \"M10 4 8.75 6.5 6 6\",\n  key: \"7mghy3\"\n}], [\"path\", {\n  d: \"m14 20 1.25-2.5L18 18\",\n  key: \"1chtki\"\n}], [\"path\", {\n  d: \"m14 4 1.25 2.5L18 6\",\n  key: \"1b4wsy\"\n}], [\"path\", {\n  d: \"m17 21-3-6h-4\",\n  key: \"15hhxa\"\n}], [\"path\", {\n  d: \"m17 3-3 6 1.5 3\",\n  key: \"11697g\"\n}], [\"path\", {\n  d: \"M2 12h6.5L10 9\",\n  key: \"kv9z4n\"\n}], [\"path\", {\n  d: \"m20 10-1.5 2 1.5 2\",\n  key: \"1swlpi\"\n}], [\"path\", {\n  d: \"M22 12h-6.5L14 15\",\n  key: \"1mxi28\"\n}], [\"path\", {\n  d: \"m4 10 1.5 2L4 14\",\n  key: \"k9enpj\"\n}], [\"path\", {\n  d: \"m7 21 3-6-1.5-3\",\n  key: \"j8hb9u\"\n}], [\"path\", {\n  d: \"m7 3 3 6h4\",\n  key: \"1otusx\"\n}]];\nconst Snowflake = createLucideIcon(\"snowflake\", __iconNode);\nexport { __iconNode, Snowflake as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Snowflake", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\snowflake.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm10 20-1.25-2.5L6 18', key: '18frcb' }],\n  ['path', { d: 'M10 4 8.75 6.5 6 6', key: '7mghy3' }],\n  ['path', { d: 'm14 20 1.25-2.5L18 18', key: '1chtki' }],\n  ['path', { d: 'm14 4 1.25 2.5L18 6', key: '1b4wsy' }],\n  ['path', { d: 'm17 21-3-6h-4', key: '15hhxa' }],\n  ['path', { d: 'm17 3-3 6 1.5 3', key: '11697g' }],\n  ['path', { d: 'M2 12h6.5L10 9', key: 'kv9z4n' }],\n  ['path', { d: 'm20 10-1.5 2 1.5 2', key: '1swlpi' }],\n  ['path', { d: 'M22 12h-6.5L14 15', key: '1mxi28' }],\n  ['path', { d: 'm4 10 1.5 2L4 14', key: 'k9enpj' }],\n  ['path', { d: 'm7 21 3-6-1.5-3', key: 'j8hb9u' }],\n  ['path', { d: 'm7 3 3 6h4', key: '1otusx' }],\n];\n\n/**\n * @component @name Snowflake\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTAgMjAtMS4yNS0yLjVMNiAxOCIgLz4KICA8cGF0aCBkPSJNMTAgNCA4Ljc1IDYuNSA2IDYiIC8+CiAgPHBhdGggZD0ibTE0IDIwIDEuMjUtMi41TDE4IDE4IiAvPgogIDxwYXRoIGQ9Im0xNCA0IDEuMjUgMi41TDE4IDYiIC8+CiAgPHBhdGggZD0ibTE3IDIxLTMtNmgtNCIgLz4KICA8cGF0aCBkPSJtMTcgMy0zIDYgMS41IDMiIC8+CiAgPHBhdGggZD0iTTIgMTJoNi41TDEwIDkiIC8+CiAgPHBhdGggZD0ibTIwIDEwLTEuNSAyIDEuNSAyIiAvPgogIDxwYXRoIGQ9Ik0yMiAxMmgtNi41TDE0IDE1IiAvPgogIDxwYXRoIGQ9Im00IDEwIDEuNSAyTDQgMTQiIC8+CiAgPHBhdGggZD0ibTcgMjEgMy02LTEuNS0zIiAvPgogIDxwYXRoIGQ9Im03IDMgMyA2aDQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/snowflake\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Snowflake = createLucideIcon('snowflake', __iconNode);\n\nexport default Snowflake;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAwBC,GAAA,EAAK;AAAA,CAAU,GACrD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAsBC,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAyBC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAuBC,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAiBC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAmBC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAkBC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAsBC,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAqBC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAoBC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAmBC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAcC,GAAA,EAAK;AAAA,CAAU,EAC7C;AAaA,MAAMC,SAAA,GAAYC,gBAAA,CAAiB,aAAaJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}