import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { API_CONFIG, STORAGE_KEYS, MOCK_RESPONSES } from '../config/api';

// Create axios instance with default configuration
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: API_CONFIG.BASE_URL,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor to add auth token
  instance.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor for error handling
  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response?.status === 401) {
        // Token expired, redirect to login
        localStorage.removeItem(STORAGE_KEYS.TOKEN);
        localStorage.removeItem(STORAGE_KEYS.USER);
        window.location.href = '/login';
      }
      return Promise.reject(error);
    }
  );

  return instance;
};

const api = createApiInstance();

// Generic API service class
class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = api;
  }

  // Generic GET request
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.get<T>(url, config);
    return response.data;
  }

  // Generic POST request
  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.post<T>(url, data, config);
    return response.data;
  }

  // Generic PUT request
  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.put<T>(url, data, config);
    return response.data;
  }

  // Generic DELETE request
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.delete<T>(url, config);
    return response.data;
  }

  // Generic PATCH request
  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.patch<T>(url, data, config);
    return response.data;
  }
}

// Create service instance
const apiService = new ApiService();

// Dashboard API calls
export const dashboardApi = {
  // Get dashboard statistics
  async getStats(): Promise<any> {
    try {
      // For demo purposes, return mock data
      // Replace with: return apiService.get(API_CONFIG.DASHBOARD.STATS);
      return new Promise(resolve => {
        setTimeout(() => resolve(MOCK_RESPONSES.DASHBOARD_STATS), 500);
      });
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      throw error;
    }
  },

  // Get recent activity
  async getRecentActivity(): Promise<any[]> {
    try {
      // For demo purposes, return mock data
      // Replace with: return apiService.get(API_CONFIG.DASHBOARD.RECENT_ACTIVITY);
      return new Promise(resolve => {
        setTimeout(() => resolve(MOCK_RESPONSES.RECENT_ACTIVITY), 500);
      });
    } catch (error) {
      console.error('Error fetching recent activity:', error);
      throw error;
    }
  },

  // Get chart data
  async getChartData(): Promise<any> {
    try {
      // For demo purposes, return mock data
      // Replace with: return apiService.get(API_CONFIG.DASHBOARD.CHARTS);
      const mockChartData = {
        userGrowth: [
          { month: 'Jan', users: 100 },
          { month: 'Feb', users: 150 },
          { month: 'Mar', users: 200 },
          { month: 'Apr', users: 280 },
          { month: 'May', users: 350 },
          { month: 'Jun', users: 420 },
        ],
        postActivity: [
          { day: 'Mon', posts: 12 },
          { day: 'Tue', posts: 19 },
          { day: 'Wed', posts: 15 },
          { day: 'Thu', posts: 25 },
          { day: 'Fri', posts: 22 },
          { day: 'Sat', posts: 18 },
          { day: 'Sun', posts: 14 },
        ],
      };
      return new Promise(resolve => {
        setTimeout(() => resolve(mockChartData), 500);
      });
    } catch (error) {
      console.error('Error fetching chart data:', error);
      throw error;
    }
  },
};

// Users API calls
export const usersApi = {
  // Get all users
  async getUsers(page = 1, limit = 10): Promise<any> {
    try {
      // For demo purposes, use JSONPlaceholder API
      // Replace with: return apiService.get(`${API_CONFIG.USERS.LIST}?page=${page}&limit=${limit}`);
      const response = await apiService.get(`${API_CONFIG.USERS.LIST}?_page=${page}&_limit=${limit}`);
      return response;
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  },

  // Get user by ID
  async getUser(id: string): Promise<any> {
    try {
      return apiService.get(`${API_CONFIG.USERS.LIST}/${id}`);
    } catch (error) {
      console.error('Error fetching user:', error);
      throw error;
    }
  },

  // Create user
  async createUser(userData: any): Promise<any> {
    try {
      return apiService.post(API_CONFIG.USERS.CREATE, userData);
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  },

  // Update user
  async updateUser(id: string, userData: any): Promise<any> {
    try {
      return apiService.put(`${API_CONFIG.USERS.UPDATE}/${id}`, userData);
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  },

  // Delete user
  async deleteUser(id: string): Promise<any> {
    try {
      return apiService.delete(`${API_CONFIG.USERS.DELETE}/${id}`);
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  },
};

// Posts API calls
export const postsApi = {
  // Get all posts
  async getPosts(page = 1, limit = 10): Promise<any> {
    try {
      return apiService.get(`${API_CONFIG.POSTS}?_page=${page}&_limit=${limit}`);
    } catch (error) {
      console.error('Error fetching posts:', error);
      throw error;
    }
  },

  // Get post by ID
  async getPost(id: string): Promise<any> {
    try {
      return apiService.get(`${API_CONFIG.POSTS}/${id}`);
    } catch (error) {
      console.error('Error fetching post:', error);
      throw error;
    }
  },
};

// Comments API calls
export const commentsApi = {
  // Get all comments
  async getComments(page = 1, limit = 10): Promise<any> {
    try {
      return apiService.get(`${API_CONFIG.COMMENTS}?_page=${page}&_limit=${limit}`);
    } catch (error) {
      console.error('Error fetching comments:', error);
      throw error;
    }
  },
};

export default apiService;
