{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M9 18V5l12-2v13\",\n  key: \"1jmyc2\"\n}], [\"path\", {\n  d: \"m9 9 12-2\",\n  key: \"1e64n2\"\n}], [\"circle\", {\n  cx: \"6\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"fqmcym\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"16\",\n  r: \"3\",\n  key: \"1hluhg\"\n}]];\nconst Music4 = createLucideIcon(\"music-4\", __iconNode);\nexport { __iconNode, Music4 as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "Music4", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\music-4.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M9 18V5l12-2v13', key: '1jmyc2' }],\n  ['path', { d: 'm9 9 12-2', key: '1e64n2' }],\n  ['circle', { cx: '6', cy: '18', r: '3', key: 'fqmcym' }],\n  ['circle', { cx: '18', cy: '16', r: '3', key: '1hluhg' }],\n];\n\n/**\n * @component @name Music4\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOSAxOFY1bDEyLTJ2MTMiIC8+CiAgPHBhdGggZD0ibTkgOSAxMi0yIiAvPgogIDxjaXJjbGUgY3g9IjYiIGN5PSIxOCIgcj0iMyIgLz4KICA8Y2lyY2xlIGN4PSIxOCIgY3k9IjE2IiByPSIzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/music-4\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Music4 = createLucideIcon('music-4', __iconNode);\n\nexport default Music4;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAmBC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,UAAU;EAAEC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAMC,CAAA,EAAG;EAAKH,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,UAAU;EAAEC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,CAAA,EAAG;EAAKH,GAAA,EAAK;AAAA,CAAU,EAC1D;AAaA,MAAMI,MAAA,GAASC,gBAAA,CAAiB,WAAWP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}