{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2.992 16.342a2 2 0 0 1 .094 1.167l-1.065 3.29a1 1 0 0 0 1.236 1.168l3.413-.998a2 2 0 0 1 1.099.092 10 10 0 1 0-4.777-4.719\",\n  key: \"1sd12s\"\n}], [\"path\", {\n  d: \"M7.828 13.07A3 3 0 0 1 12 8.764a3 3 0 0 1 5.004 2.224 3 3 0 0 1-.832 2.083l-3.447 3.62a1 1 0 0 1-1.45-.001z\",\n  key: \"hoo97p\"\n}]];\nconst MessageCircleHeart = createLucideIcon(\"message-circle-heart\", __iconNode);\nexport { __iconNode, MessageCircleHeart as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "MessageCircleHeart", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\message-circle-heart.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M2.992 16.342a2 2 0 0 1 .094 1.167l-1.065 3.29a1 1 0 0 0 1.236 1.168l3.413-.998a2 2 0 0 1 1.099.092 10 10 0 1 0-4.777-4.719',\n      key: '1sd12s',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M7.828 13.07A3 3 0 0 1 12 8.764a3 3 0 0 1 5.004 2.224 3 3 0 0 1-.832 2.083l-3.447 3.62a1 1 0 0 1-1.45-.001z',\n      key: 'hoo97p',\n    },\n  ],\n];\n\n/**\n * @component @name MessageCircleHeart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMi45OTIgMTYuMzQyYTIgMiAwIDAgMSAuMDk0IDEuMTY3bC0xLjA2NSAzLjI5YTEgMSAwIDAgMCAxLjIzNiAxLjE2OGwzLjQxMy0uOTk4YTIgMiAwIDAgMSAxLjA5OS4wOTIgMTAgMTAgMCAxIDAtNC43NzctNC43MTkiIC8+CiAgPHBhdGggZD0iTTcuODI4IDEzLjA3QTMgMyAwIDAgMSAxMiA4Ljc2NGEzIDMgMCAwIDEgNS4wMDQgMi4yMjQgMyAzIDAgMCAxLS44MzIgMi4wODNsLTMuNDQ3IDMuNjJhMSAxIDAgMCAxLTEuNDUtLjAwMXoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/message-circle-heart\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageCircleHeart = createLucideIcon('message-circle-heart', __iconNode);\n\nexport default MessageCircleHeart;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CACE,QACA;EACEC,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EAET,EACA,CACE,QACA;EACED,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EACP,CAEJ;AAaA,MAAMC,kBAAA,GAAqBC,gBAAA,CAAiB,wBAAwBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}