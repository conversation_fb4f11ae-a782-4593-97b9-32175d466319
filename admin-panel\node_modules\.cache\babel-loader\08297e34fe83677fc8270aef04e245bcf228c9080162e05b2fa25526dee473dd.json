{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 10h6V4\",\n  key: \"zwrco\"\n}], [\"path\", {\n  d: \"m2 4 6 6\",\n  key: \"ug085t\"\n}], [\"path\", {\n  d: \"M21 10V7a2 2 0 0 0-2-2h-7\",\n  key: \"git5jr\"\n}], [\"path\", {\n  d: \"M3 14v2a2 2 0 0 0 2 2h3\",\n  key: \"1f7fh3\"\n}], [\"rect\", {\n  x: \"12\",\n  y: \"14\",\n  width: \"10\",\n  height: \"7\",\n  rx: \"1\",\n  key: \"1wjs3o\"\n}]];\nconst PictureInPicture = createLucideIcon(\"picture-in-picture\", __iconNode);\nexport { __iconNode, PictureInPicture as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x", "y", "width", "height", "rx", "PictureInPicture", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\picture-in-picture.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M2 10h6V4', key: 'zwrco' }],\n  ['path', { d: 'm2 4 6 6', key: 'ug085t' }],\n  ['path', { d: 'M21 10V7a2 2 0 0 0-2-2h-7', key: 'git5jr' }],\n  ['path', { d: 'M3 14v2a2 2 0 0 0 2 2h3', key: '1f7fh3' }],\n  ['rect', { x: '12', y: '14', width: '10', height: '7', rx: '1', key: '1wjs3o' }],\n];\n\n/**\n * @component @name PictureInPicture\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAxMGg2VjQiIC8+CiAgPHBhdGggZD0ibTIgNCA2IDYiIC8+CiAgPHBhdGggZD0iTTIxIDEwVjdhMiAyIDAgMCAwLTItMmgtNyIgLz4KICA8cGF0aCBkPSJNMyAxNHYyYTIgMiAwIDAgMCAyIDJoMyIgLz4KICA8cmVjdCB4PSIxMiIgeT0iMTQiIHdpZHRoPSIxMCIgaGVpZ2h0PSI3IiByeD0iMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/picture-in-picture\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PictureInPicture = createLucideIcon('picture-in-picture', __iconNode);\n\nexport default PictureInPicture;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAS,GACzC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA6BC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA2BC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAMC,CAAA,EAAG;EAAMC,KAAA,EAAO;EAAMC,MAAA,EAAQ;EAAKC,EAAA,EAAI;EAAKL,GAAA,EAAK;AAAA,CAAU,EACjF;AAaA,MAAMM,gBAAA,GAAmBC,gBAAA,CAAiB,sBAAsBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}