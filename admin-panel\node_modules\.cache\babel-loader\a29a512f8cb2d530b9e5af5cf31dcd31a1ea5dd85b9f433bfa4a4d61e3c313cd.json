{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 17a2 2 0 0 1-2 2H6.828a2 2 0 0 0-1.414.586l-2.202 2.202A.71.71 0 0 1 2 21.286V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2z\",\n  key: \"18887p\"\n}], [\"path\", {\n  d: \"M7.5 9.5c0 .687.265 1.383.697 1.844l3.009 3.264a1.14 1.14 0 0 0 .407.314 1 1 0 0 0 .783-.004 1.14 1.14 0 0 0 .398-.31l3.008-3.264A2.77 2.77 0 0 0 16.5 9.5 2.5 2.5 0 0 0 12 8a2.5 2.5 0 0 0-4.5 1.5\",\n  key: \"1faxuh\"\n}]];\nconst MessageSquareHeart = createLucideIcon(\"message-square-heart\", __iconNode);\nexport { __iconNode, MessageSquareHeart as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "MessageSquareHeart", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\message-square-heart.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M22 17a2 2 0 0 1-2 2H6.828a2 2 0 0 0-1.414.586l-2.202 2.202A.71.71 0 0 1 2 21.286V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2z',\n      key: '18887p',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M7.5 9.5c0 .687.265 1.383.697 1.844l3.009 3.264a1.14 1.14 0 0 0 .407.314 1 1 0 0 0 .783-.004 1.14 1.14 0 0 0 .398-.31l3.008-3.264A2.77 2.77 0 0 0 16.5 9.5 2.5 2.5 0 0 0 12 8a2.5 2.5 0 0 0-4.5 1.5',\n      key: '1faxuh',\n    },\n  ],\n];\n\n/**\n * @component @name MessageSquareHeart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMTdhMiAyIDAgMCAxLTIgMkg2LjgyOGEyIDIgMCAwIDAtMS40MTQuNTg2bC0yLjIwMiAyLjIwMkEuNzEuNzEgMCAwIDEgMiAyMS4yODZWNWEyIDIgMCAwIDEgMi0yaDE2YTIgMiAwIDAgMSAyIDJ6IiAvPgogIDxwYXRoIGQ9Ik03LjUgOS41YzAgLjY4Ny4yNjUgMS4zODMuNjk3IDEuODQ0bDMuMDA5IDMuMjY0YTEuMTQgMS4xNCAwIDAgMCAuNDA3LjMxNCAxIDEgMCAwIDAgLjc4My0uMDA0IDEuMTQgMS4xNCAwIDAgMCAuMzk4LS4zMWwzLjAwOC0zLjI2NEEyLjc3IDIuNzcgMCAwIDAgMTYuNSA5LjUgMi41IDIuNSAwIDAgMCAxMiA4YTIuNSAyLjUgMCAwIDAtNC41IDEuNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/message-square-heart\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageSquareHeart = createLucideIcon('message-square-heart', __iconNode);\n\nexport default MessageSquareHeart;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CACE,QACA;EACEC,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EAET,EACA,CACE,QACA;EACED,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EACP,CAEJ;AAaA,MAAMC,kBAAA,GAAqBC,gBAAA,CAAiB,wBAAwBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}