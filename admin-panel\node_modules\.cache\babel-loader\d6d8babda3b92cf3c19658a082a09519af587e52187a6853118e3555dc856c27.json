{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 8.5V5a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v16.286a.71.71 0 0 0 1.212.502l2.202-2.202A2 2 0 0 1 6.828 19H10\",\n  key: \"fu6chl\"\n}], [\"path\", {\n  d: \"M20 15v-2a2 2 0 0 0-4 0v2\",\n  key: \"vl8a78\"\n}], [\"rect\", {\n  x: \"14\",\n  y: \"15\",\n  width: \"8\",\n  height: \"5\",\n  rx: \"1\",\n  key: \"37aafw\"\n}]];\nconst MessageSquareLock = createLucideIcon(\"message-square-lock\", __iconNode);\nexport { __iconNode, MessageSquareLock as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x", "y", "width", "height", "rx", "MessageSquareLock", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\message-square-lock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M22 8.5V5a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v16.286a.71.71 0 0 0 1.212.502l2.202-2.202A2 2 0 0 1 6.828 19H10',\n      key: 'fu6chl',\n    },\n  ],\n  ['path', { d: 'M20 15v-2a2 2 0 0 0-4 0v2', key: 'vl8a78' }],\n  ['rect', { x: '14', y: '15', width: '8', height: '5', rx: '1', key: '37aafw' }],\n];\n\n/**\n * @component @name MessageSquareLock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgOC41VjVhMiAyIDAgMCAwLTItMkg0YTIgMiAwIDAgMC0yIDJ2MTYuMjg2YS43MS43MSAwIDAgMCAxLjIxMi41MDJsMi4yMDItMi4yMDJBMiAyIDAgMCAxIDYuODI4IDE5SDEwIiAvPgogIDxwYXRoIGQ9Ik0yMCAxNXYtMmEyIDIgMCAwIDAtNCAwdjIiIC8+CiAgPHJlY3QgeD0iMTQiIHk9IjE1IiB3aWR0aD0iOCIgaGVpZ2h0PSI1IiByeD0iMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/message-square-lock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageSquareLock = createLucideIcon('message-square-lock', __iconNode);\n\nexport default MessageSquareLock;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CACE,QACA;EACEC,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EAET,EACA,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA6BC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAMC,CAAA,EAAG;EAAMC,KAAA,EAAO;EAAKC,MAAA,EAAQ;EAAKC,EAAA,EAAI;EAAKL,GAAA,EAAK;AAAA,CAAU,EAChF;AAaA,MAAMM,iBAAA,GAAoBC,gBAAA,CAAiB,uBAAuBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}