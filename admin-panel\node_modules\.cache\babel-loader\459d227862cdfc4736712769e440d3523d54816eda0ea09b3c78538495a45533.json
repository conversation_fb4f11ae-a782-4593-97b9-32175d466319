{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m21 17-2.156-1.868A.5.5 0 0 0 18 15.5v.5a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1c0-2.545-3.991-3.97-8.5-4a1 1 0 0 0 0 5c4.153 0 4.745-11.295 5.708-13.5a2.5 2.5 0 1 1 3.31 3.284\",\n  key: \"y32ogt\"\n}], [\"path\", {\n  d: \"M3 21h18\",\n  key: \"itz85i\"\n}]];\nconst Signature = createLucideIcon(\"signature\", __iconNode);\nexport { __iconNode, Signature as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Signature", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\signature.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm21 17-2.156-1.868A.5.5 0 0 0 18 15.5v.5a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1c0-2.545-3.991-3.97-8.5-4a1 1 0 0 0 0 5c4.153 0 4.745-11.295 5.708-13.5a2.5 2.5 0 1 1 3.31 3.284',\n      key: 'y32ogt',\n    },\n  ],\n  ['path', { d: 'M3 21h18', key: 'itz85i' }],\n];\n\n/**\n * @component @name Signature\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEgMTctMi4xNTYtMS44NjhBLjUuNSAwIDAgMCAxOCAxNS41di41YTEgMSAwIDAgMS0xIDFoLTJhMSAxIDAgMCAxLTEtMWMwLTIuNTQ1LTMuOTkxLTMuOTctOC41LTRhMSAxIDAgMCAwIDAgNWM0LjE1MyAwIDQuNzQ1LTExLjI5NSA1LjcwOC0xMy41YTIuNSAyLjUgMCAxIDEgMy4zMSAzLjI4NCIgLz4KICA8cGF0aCBkPSJNMyAyMWgxOCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/signature\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Signature = createLucideIcon('signature', __iconNode);\n\nexport default Signature;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CACE,QACA;EACEC,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EAET,EACA,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,EAC3C;AAaA,MAAMC,SAAA,GAAYC,gBAAA,CAAiB,aAAaJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}