import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/auth/ProtectedRoute';
import LoginForm from './components/auth/LoginForm';
import Layout from './components/layout/Layout';
import Dashboard from './pages/Dashboard';
import Users from './pages/Users';
import Posts from './pages/Posts';
import Comments from './pages/Comments';
import Settings from './pages/Settings';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Routes>
            {/* Public routes */}
            <Route path="/login" element={<LoginForm />} />

            {/* Protected routes */}
            <Route path="/" element={
              <ProtectedRoute>
                <Layout />
              </ProtectedRoute>
            }>
              <Route index element={<Navigate to="/dashboard" replace />} />
              <Route path="dashboard" element={<Dashboard />} />
              <Route path="users" element={
                <ProtectedRoute requiredPermissions={['read', 'admin']}>
                  <Users />
                </ProtectedRoute>
              } />
              <Route path="posts" element={<Posts />} />
              <Route path="comments" element={<Comments />} />
              <Route path="analytics" element={
                <ProtectedRoute requiredPermissions={['admin']}>
                  <div className="text-center py-12">
                    <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
                    <p className="text-gray-600 mt-2">Analytics dashboard coming soon...</p>
                  </div>
                </ProtectedRoute>
              } />
              <Route path="security" element={
                <ProtectedRoute requiredPermissions={['admin']}>
                  <div className="text-center py-12">
                    <h1 className="text-2xl font-bold text-gray-900">Security</h1>
                    <p className="text-gray-600 mt-2">Security settings coming soon...</p>
                  </div>
                </ProtectedRoute>
              } />
              <Route path="settings" element={<Settings />} />
            </Route>

            {/* Catch all route */}
            <Route path="*" element={
              <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                  <h1 className="text-4xl font-bold text-gray-900">404</h1>
                  <p className="text-gray-600 mt-2">Page not found</p>
                </div>
              </div>
            } />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
