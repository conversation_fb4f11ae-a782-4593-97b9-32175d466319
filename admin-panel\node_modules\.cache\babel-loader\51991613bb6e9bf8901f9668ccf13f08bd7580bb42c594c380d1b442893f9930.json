{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17 10h-1a4 4 0 1 1 4-4v.534\",\n  key: \"7qf5zm\"\n}], [\"path\", {\n  d: \"M17 6h1a4 4 0 0 1 1.42 7.74l-2.29.87a6 6 0 0 1-5.339-10.68l2.069-1.31\",\n  key: \"1et29u\"\n}], [\"path\", {\n  d: \"M4.5 17c2.8-.5 4.4 0 5.5.8s1.8 2.2 2.3 3.7c-2 .4-3.5.4-4.8-.3-1.2-.6-2.3-1.9-3-4.2\",\n  key: \"kiv2lz\"\n}], [\"path\", {\n  d: \"M9.77 12C4 15 2 22 2 22\",\n  key: \"h28rw0\"\n}], [\"circle\", {\n  cx: \"17\",\n  cy: \"8\",\n  r: \"2\",\n  key: \"1330xn\"\n}]];\nconst Rose = createLucideIcon(\"rose\", __iconNode);\nexport { __iconNode, Rose as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "<PERSON>", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\rose.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M17 10h-1a4 4 0 1 1 4-4v.534', key: '7qf5zm' }],\n  [\n    'path',\n    { d: 'M17 6h1a4 4 0 0 1 1.42 7.74l-2.29.87a6 6 0 0 1-5.339-10.68l2.069-1.31', key: '1et29u' },\n  ],\n  [\n    'path',\n    {\n      d: 'M4.5 17c2.8-.5 4.4 0 5.5.8s1.8 2.2 2.3 3.7c-2 .4-3.5.4-4.8-.3-1.2-.6-2.3-1.9-3-4.2',\n      key: 'kiv2lz',\n    },\n  ],\n  ['path', { d: 'M9.77 12C4 15 2 22 2 22', key: 'h28rw0' }],\n  ['circle', { cx: '17', cy: '8', r: '2', key: '1330xn' }],\n];\n\n/**\n * @component @name Rose\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTcgMTBoLTFhNCA0IDAgMSAxIDQtNHYuNTM0IiAvPgogIDxwYXRoIGQ9Ik0xNyA2aDFhNCA0IDAgMCAxIDEuNDIgNy43NGwtMi4yOS44N2E2IDYgMCAwIDEtNS4zMzktMTAuNjhsMi4wNjktMS4zMSIgLz4KICA8cGF0aCBkPSJNNC41IDE3YzIuOC0uNSA0LjQgMCA1LjUuOHMxLjggMi4yIDIuMyAzLjdjLTIgLjQtMy41LjQtNC44LS4zLTEuMi0uNi0yLjMtMS45LTMtNC4yIiAvPgogIDxwYXRoIGQ9Ik05Ljc3IDEyQzQgMTUgMiAyMiAyIDIyIiAvPgogIDxjaXJjbGUgY3g9IjE3IiBjeT0iOCIgcj0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/rose\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Rose = createLucideIcon('rose', __iconNode);\n\nexport default Rose;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAgCC,GAAA,EAAK;AAAA,CAAU,GAC7D,CACE,QACA;EAAED,CAAA,EAAG;EAAyEC,GAAA,EAAK;AAAA,EACrF,EACA,CACE,QACA;EACED,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EAET,EACA,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA2BC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,UAAU;EAAEC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAKC,CAAA,EAAG;EAAKH,GAAA,EAAK;AAAA,CAAU,EACzD;AAaA,MAAMI,IAAA,GAAOC,gBAAA,CAAiB,QAAQP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}