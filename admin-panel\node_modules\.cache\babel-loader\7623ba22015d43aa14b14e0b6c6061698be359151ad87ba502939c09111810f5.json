{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 16h1\",\n  key: \"1cioin\"\n}], [\"path\", {\n  d: \"M14 8h1\",\n  key: \"1lfen6\"\n}], [\"path\", {\n  d: \"M19 16h2\",\n  key: \"qbhnk2\"\n}], [\"path\", {\n  d: \"M19 8h2\",\n  key: \"1jleli\"\n}], [\"path\", {\n  d: \"M3 16h2\",\n  key: \"1r0mgj\"\n}], [\"path\", {\n  d: \"M3 8h2\",\n  key: \"1ndivp\"\n}], [\"path\", {\n  d: \"M9 16h1\",\n  key: \"1wweuk\"\n}], [\"path\", {\n  d: \"M9 8h1\",\n  key: \"zb2d67\"\n}], [\"rect\", {\n  x: \"3\",\n  y: \"3\",\n  width: \"18\",\n  height: \"18\",\n  rx: \"2\",\n  key: \"h1oib\"\n}]];\nconst PanelTopBottomDashed = createLucideIcon(\"panel-top-bottom-dashed\", __iconNode);\nexport { __iconNode, PanelTopBottomDashed as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x", "y", "width", "height", "rx", "PanelTopBottomDashed", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\panel-top-bottom-dashed.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M14 16h1', key: '1cioin' }],\n  ['path', { d: 'M14 8h1', key: '1lfen6' }],\n  ['path', { d: 'M19 16h2', key: 'qbhnk2' }],\n  ['path', { d: 'M19 8h2', key: '1jleli' }],\n  ['path', { d: 'M3 16h2', key: '1r0mgj' }],\n  ['path', { d: 'M3 8h2', key: '1ndivp' }],\n  ['path', { d: 'M9 16h1', key: '1wweuk' }],\n  ['path', { d: 'M9 8h1', key: 'zb2d67' }],\n  ['rect', { x: '3', y: '3', width: '18', height: '18', rx: '2', key: 'h1oib' }],\n];\n\n/**\n * @component @name PanelTopBottomDashed\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQgMTZoMSIgLz4KICA8cGF0aCBkPSJNMTQgOGgxIiAvPgogIDxwYXRoIGQ9Ik0xOSAxNmgyIiAvPgogIDxwYXRoIGQ9Ik0xOSA4aDIiIC8+CiAgPHBhdGggZD0iTTMgMTZoMiIgLz4KICA8cGF0aCBkPSJNMyA4aDIiIC8+CiAgPHBhdGggZD0iTTkgMTZoMSIgLz4KICA8cGF0aCBkPSJNOSA4aDEiIC8+CiAgPHJlY3QgeD0iMyIgeT0iMyIgd2lkdGg9IjE4IiBoZWlnaHQ9IjE4IiByeD0iMiIgLz4KPC9zdmc+) - https://lucide.dev/icons/panel-top-bottom-dashed\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PanelTopBottomDashed = createLucideIcon('panel-top-bottom-dashed', __iconNode);\n\nexport default PanelTopBottomDashed;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAUC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAUC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAKC,CAAA,EAAG;EAAKC,KAAA,EAAO;EAAMC,MAAA,EAAQ;EAAMC,EAAA,EAAI;EAAKL,GAAA,EAAK;AAAA,CAAS,EAC/E;AAaA,MAAMM,oBAAA,GAAuBC,gBAAA,CAAiB,2BAA2BT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}