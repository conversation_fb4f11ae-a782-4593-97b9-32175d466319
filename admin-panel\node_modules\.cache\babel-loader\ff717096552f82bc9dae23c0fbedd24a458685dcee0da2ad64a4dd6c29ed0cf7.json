{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 17a1 1 0 0 0-1 1v1a2 2 0 1 0 2-2z\",\n  key: \"skzb1g\"\n}], [\"path\", {\n  d: \"M20.97 3.61a.45.45 0 0 0-.58-.58C10.2 6.6 6.6 10.2 3.03 20.39a.45.45 0 0 0 .58.58C13.8 17.4 17.4 13.8 20.97 3.61\",\n  key: \"cv9jm7\"\n}], [\"path\", {\n  d: \"m6.707 6.707 10.586 10.586\",\n  key: \"d2l993\"\n}], [\"path\", {\n  d: \"M7 5a2 2 0 1 0-2 2h1a1 1 0 0 0 1-1z\",\n  key: \"i0et4n\"\n}]];\nconst Kay<PERSON> = createLucideIcon(\"kayak\", __iconNode);\nexport { __iconNode, Kayak as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "<PERSON><PERSON>", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\kayak.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M18 17a1 1 0 0 0-1 1v1a2 2 0 1 0 2-2z', key: 'skzb1g' }],\n  [\n    'path',\n    {\n      d: 'M20.97 3.61a.45.45 0 0 0-.58-.58C10.2 6.6 6.6 10.2 3.03 20.39a.45.45 0 0 0 .58.58C13.8 17.4 17.4 13.8 20.97 3.61',\n      key: 'cv9jm7',\n    },\n  ],\n  ['path', { d: 'm6.707 6.707 10.586 10.586', key: 'd2l993' }],\n  ['path', { d: 'M7 5a2 2 0 1 0-2 2h1a1 1 0 0 0 1-1z', key: 'i0et4n' }],\n];\n\n/**\n * @component @name Kayak\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggMTdhMSAxIDAgMCAwLTEgMXYxYTIgMiAwIDEgMCAyLTJ6IiAvPgogIDxwYXRoIGQ9Ik0yMC45NyAzLjYxYS40NS40NSAwIDAgMC0uNTgtLjU4QzEwLjIgNi42IDYuNiAxMC4yIDMuMDMgMjAuMzlhLjQ1LjQ1IDAgMCAwIC41OC41OEMxMy44IDE3LjQgMTcuNCAxMy44IDIwLjk3IDMuNjEiIC8+CiAgPHBhdGggZD0ibTYuNzA3IDYuNzA3IDEwLjU4NiAxMC41ODYiIC8+CiAgPHBhdGggZD0iTTcgNWEyIDIgMCAxIDAtMiAyaDFhMSAxIDAgMCAwIDEtMXoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/kayak\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Kayak = createLucideIcon('kayak', __iconNode);\n\nexport default Kayak;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAyCC,GAAA,EAAK;AAAA,CAAU,GACtE,CACE,QACA;EACED,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EAET,EACA,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA8BC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAuCC,GAAA,EAAK;AAAA,CAAU,EACtE;AAaA,MAAMC,KAAA,GAAQC,gBAAA,CAAiB,SAASJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}