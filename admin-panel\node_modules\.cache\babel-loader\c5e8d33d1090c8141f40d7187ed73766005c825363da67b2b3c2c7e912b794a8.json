{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.5 17h1.227a2 2 0 0 0 1.345-.52L18 12\",\n  key: \"16muxl\"\n}], [\"path\", {\n  d: \"m12 13.5 3.75.5\",\n  key: \"1i9qhk\"\n}], [\"path\", {\n  d: \"m4.5 8 10.58-5.06a1 1 0 0 1 1.342.488L18.5 8\",\n  key: \"12lg5p\"\n}], [\"path\", {\n  d: \"M6 10V8\",\n  key: \"1y41hn\"\n}], [\"path\", {\n  d: \"M6 14v1\",\n  key: \"cao2tf\"\n}], [\"path\", {\n  d: \"M6 19v2\",\n  key: \"1loha6\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"8\",\n  width: \"20\",\n  height: \"13\",\n  rx: \"2\",\n  key: \"p3bz5l\"\n}]];\nconst TicketsPlane = createLucideIcon(\"tickets-plane\", __iconNode);\nexport { __iconNode, TicketsPlane as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x", "y", "width", "height", "rx", "TicketsPlane", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\tickets-plane.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10.5 17h1.227a2 2 0 0 0 1.345-.52L18 12', key: '16muxl' }],\n  ['path', { d: 'm12 13.5 3.75.5', key: '1i9qhk' }],\n  ['path', { d: 'm4.5 8 10.58-5.06a1 1 0 0 1 1.342.488L18.5 8', key: '12lg5p' }],\n  ['path', { d: 'M6 10V8', key: '1y41hn' }],\n  ['path', { d: 'M6 14v1', key: 'cao2tf' }],\n  ['path', { d: 'M6 19v2', key: '1loha6' }],\n  ['rect', { x: '2', y: '8', width: '20', height: '13', rx: '2', key: 'p3bz5l' }],\n];\n\n/**\n * @component @name TicketsPlane\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAuNSAxN2gxLjIyN2EyIDIgMCAwIDAgMS4zNDUtLjUyTDE4IDEyIiAvPgogIDxwYXRoIGQ9Im0xMiAxMy41IDMuNzUuNSIgLz4KICA8cGF0aCBkPSJtNC41IDggMTAuNTgtNS4wNmExIDEgMCAwIDEgMS4zNDIuNDg4TDE4LjUgOCIgLz4KICA8cGF0aCBkPSJNNiAxMFY4IiAvPgogIDxwYXRoIGQ9Ik02IDE0djEiIC8+CiAgPHBhdGggZD0iTTYgMTl2MiIgLz4KICA8cmVjdCB4PSIyIiB5PSI4IiB3aWR0aD0iMjAiIGhlaWdodD0iMTMiIHJ4PSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/tickets-plane\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TicketsPlane = createLucideIcon('tickets-plane', __iconNode);\n\nexport default TicketsPlane;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAA4CC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAmBC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAgDC,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAKC,CAAA,EAAG;EAAKC,KAAA,EAAO;EAAMC,MAAA,EAAQ;EAAMC,EAAA,EAAI;EAAKL,GAAA,EAAK;AAAA,CAAU,EAChF;AAaA,MAAMM,YAAA,GAAeC,gBAAA,CAAiB,iBAAiBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}