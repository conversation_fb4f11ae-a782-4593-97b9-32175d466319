{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 2v4\",\n  key: \"1cmpym\"\n}], [\"path\", {\n  d: \"M12 2v4\",\n  key: \"3427ic\"\n}], [\"path\", {\n  d: \"M16 2v4\",\n  key: \"4m81vk\"\n}], [\"rect\", {\n  width: \"16\",\n  height: \"18\",\n  x: \"4\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"1u9h20\"\n}], [\"path\", {\n  d: \"M8 10h6\",\n  key: \"3oa6kw\"\n}], [\"path\", {\n  d: \"M8 14h8\",\n  key: \"1fgep2\"\n}], [\"path\", {\n  d: \"M8 18h5\",\n  key: \"17enja\"\n}]];\nconst NotepadText = createLucideIcon(\"notepad-text\", __iconNode);\nexport { __iconNode, NotepadText as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "width", "height", "x", "y", "rx", "NotepadText", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\notepad-text.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 2v4', key: '1cmpym' }],\n  ['path', { d: 'M12 2v4', key: '3427ic' }],\n  ['path', { d: 'M16 2v4', key: '4m81vk' }],\n  ['rect', { width: '16', height: '18', x: '4', y: '4', rx: '2', key: '1u9h20' }],\n  ['path', { d: 'M8 10h6', key: '3oa6kw' }],\n  ['path', { d: 'M8 14h8', key: '1fgep2' }],\n  ['path', { d: 'M8 18h5', key: '17enja' }],\n];\n\n/**\n * @component @name NotepadText\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAydjQiIC8+CiAgPHBhdGggZD0iTTEyIDJ2NCIgLz4KICA8cGF0aCBkPSJNMTYgMnY0IiAvPgogIDxyZWN0IHdpZHRoPSIxNiIgaGVpZ2h0PSIxOCIgeD0iNCIgeT0iNCIgcng9IjIiIC8+CiAgPHBhdGggZD0iTTggMTBoNiIgLz4KICA8cGF0aCBkPSJNOCAxNGg4IiAvPgogIDxwYXRoIGQ9Ik04IDE4aDUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/notepad-text\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst NotepadText = createLucideIcon('notepad-text', __iconNode);\n\nexport default NotepadText;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAUC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAA,EAAQ;EAAMC,CAAA,EAAG;EAAKC,CAAA,EAAG;EAAKC,EAAA,EAAI;EAAKL,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,EAC1C;AAaA,MAAMM,WAAA,GAAcC,gBAAA,CAAiB,gBAAgBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}