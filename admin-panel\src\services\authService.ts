import axios from 'axios';
import { API_CONFIG, STORAGE_KEYS, MOCK_RESPONSES } from '../config/api';
import { LoginCredentials, AuthResponse, User } from '../types/auth';

// Create axios instance
const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: 10000,
});

// Add token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle token refresh on 401
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Token expired, try to refresh or logout
      const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
      if (refreshToken) {
        try {
          const response = await refreshAuthToken(refreshToken);
          localStorage.setItem(STORAGE_KEYS.TOKEN, response.token);
          // Retry the original request
          error.config.headers.Authorization = `Bearer ${response.token}`;
          return api.request(error.config);
        } catch (refreshError) {
          // Refresh failed, logout user
          logout();
        }
      } else {
        logout();
      }
    }
    return Promise.reject(error);
  }
);

export const authService = {
  // Login function - Replace with your actual API call
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      // For demo purposes, we'll use a mock response
      // Replace this with actual API call:
      // const response = await api.post(API_CONFIG.AUTH.LOGIN, credentials);
      
      // Mock login logic - remove this and uncomment above for real API
      if (credentials.email === '<EMAIL>' && credentials.password === 'password') {
        const mockResponse = MOCK_RESPONSES.LOGIN_SUCCESS;
        
        // Store tokens
        localStorage.setItem(STORAGE_KEYS.TOKEN, mockResponse.token);
        localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, mockResponse.refreshToken || '');
        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(mockResponse.user));
        
        return mockResponse;
      } else {
        throw new Error('Invalid credentials');
      }
      
      // Uncomment this for real API integration:
      // return response.data;
    } catch (error) {
      throw new Error('Login failed. Please check your credentials.');
    }
  },

  // Logout function
  async logout(): Promise<void> {
    try {
      // Call logout endpoint if needed
      // await api.post(API_CONFIG.AUTH.LOGOUT);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage
      localStorage.removeItem(STORAGE_KEYS.TOKEN);
      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER);
    }
  },

  // Get current user
  async getCurrentUser(): Promise<User | null> {
    try {
      const storedUser = localStorage.getItem(STORAGE_KEYS.USER);
      if (storedUser) {
        return JSON.parse(storedUser);
      }
      
      // For real API, uncomment this:
      // const response = await api.get(API_CONFIG.AUTH.ME);
      // return response.data;
      
      return null;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  },

  // Check if user is authenticated
  isAuthenticated(): boolean {
    const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
    return !!token;
  },

  // Get stored token
  getToken(): string | null {
    return localStorage.getItem(STORAGE_KEYS.TOKEN);
  },
};

// Refresh token function
async function refreshAuthToken(refreshToken: string): Promise<{ token: string }> {
  const response = await api.post(API_CONFIG.AUTH.REFRESH, { refreshToken });
  return response.data;
}

// Logout helper
function logout(): void {
  localStorage.removeItem(STORAGE_KEYS.TOKEN);
  localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
  localStorage.removeItem(STORAGE_KEYS.USER);
  window.location.href = '/login';
}
