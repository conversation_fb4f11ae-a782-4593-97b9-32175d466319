{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 6h3\",\n  key: \"155dbl\"\n}], [\"path\", {\n  d: \"M17 6h.01\",\n  key: \"e2y6kg\"\n}], [\"rect\", {\n  width: \"18\",\n  height: \"20\",\n  x: \"3\",\n  y: \"2\",\n  rx: \"2\",\n  key: \"od3kk9\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"13\",\n  r: \"5\",\n  key: \"nlbqau\"\n}], [\"path\", {\n  d: \"M12 18a2.5 2.5 0 0 0 0-5 2.5 2.5 0 0 1 0-5\",\n  key: \"17lach\"\n}]];\nconst WashingMachine = createLucideIcon(\"washing-machine\", __iconNode);\nexport { __iconNode, WashingMachine as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "width", "height", "x", "y", "rx", "cx", "cy", "r", "WashingMachine", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\washing-machine.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 6h3', key: '155dbl' }],\n  ['path', { d: 'M17 6h.01', key: 'e2y6kg' }],\n  ['rect', { width: '18', height: '20', x: '3', y: '2', rx: '2', key: 'od3kk9' }],\n  ['circle', { cx: '12', cy: '13', r: '5', key: 'nlbqau' }],\n  ['path', { d: 'M12 18a2.5 2.5 0 0 0 0-5 2.5 2.5 0 0 1 0-5', key: '17lach' }],\n];\n\n/**\n * @component @name WashingMachine\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyA2aDMiIC8+CiAgPHBhdGggZD0iTTE3IDZoLjAxIiAvPgogIDxyZWN0IHdpZHRoPSIxOCIgaGVpZ2h0PSIyMCIgeD0iMyIgeT0iMiIgcng9IjIiIC8+CiAgPGNpcmNsZSBjeD0iMTIiIGN5PSIxMyIgcj0iNSIgLz4KICA8cGF0aCBkPSJNMTIgMThhMi41IDIuNSAwIDAgMCAwLTUgMi41IDIuNSAwIDAgMSAwLTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/washing-machine\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst WashingMachine = createLucideIcon('washing-machine', __iconNode);\n\nexport default WashingMachine;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAUC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAA,EAAQ;EAAMC,CAAA,EAAG;EAAKC,CAAA,EAAG;EAAKC,EAAA,EAAI;EAAKL,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,UAAU;EAAEM,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,CAAA,EAAG;EAAKR,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA8CC,GAAA,EAAK;AAAA,CAAU,EAC7E;AAaA,MAAMS,cAAA,GAAiBC,gBAAA,CAAiB,mBAAmBZ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}