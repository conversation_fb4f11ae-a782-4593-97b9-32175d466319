{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12.662 21H5a2 2 0 0 1-2-2v-9a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v2.475\",\n  key: \"uubd2h\"\n}], [\"path\", {\n  d: \"M14.959 12.717A1 1 0 0 0 14 12h-4a1 1 0 0 0-1 1v8\",\n  key: \"p7f341\"\n}], [\"path\", {\n  d: \"M15 18h6\",\n  key: \"3b3c90\"\n}], [\"path\", {\n  d: \"M18 15v6\",\n  key: \"9wciyi\"\n}]];\nconst HousePlus = createLucideIcon(\"house-plus\", __iconNode);\nexport { __iconNode, HousePlus as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "HousePlus", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\house-plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M12.662 21H5a2 2 0 0 1-2-2v-9a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v2.475',\n      key: 'uubd2h',\n    },\n  ],\n  ['path', { d: 'M14.959 12.717A1 1 0 0 0 14 12h-4a1 1 0 0 0-1 1v8', key: 'p7f341' }],\n  ['path', { d: 'M15 18h6', key: '3b3c90' }],\n  ['path', { d: 'M18 15v6', key: '9wciyi' }],\n];\n\n/**\n * @component @name HousePlus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuNjYyIDIxSDVhMiAyIDAgMCAxLTItMnYtOWEyIDIgMCAwIDEgLjcwOS0xLjUyOGw3LTUuOTk5YTIgMiAwIDAgMSAyLjU4MiAwbDcgNS45OTlBMiAyIDAgMCAxIDIxIDEwdjIuNDc1IiAvPgogIDxwYXRoIGQ9Ik0xNC45NTkgMTIuNzE3QTEgMSAwIDAgMCAxNCAxMmgtNGExIDEgMCAwIDAtMSAxdjgiIC8+CiAgPHBhdGggZD0iTTE1IDE4aDYiIC8+CiAgPHBhdGggZD0iTTE4IDE1djYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/house-plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst HousePlus = createLucideIcon('house-plus', __iconNode);\n\nexport default HousePlus;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CACE,QACA;EACEC,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EAET,EACA,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAqDC,GAAA,EAAK;AAAA,CAAU,GAClF,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,EAC3C;AAaA,MAAMC,SAAA,GAAYC,gBAAA,CAAiB,cAAcJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}