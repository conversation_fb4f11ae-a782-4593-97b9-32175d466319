[{"D:\\test\\admin-panel\\src\\index.tsx": "1", "D:\\test\\admin-panel\\src\\reportWebVitals.ts": "2", "D:\\test\\admin-panel\\src\\App.tsx": "3", "D:\\test\\admin-panel\\src\\pages\\Dashboard.tsx": "4", "D:\\test\\admin-panel\\src\\contexts\\AuthContext.tsx": "5", "D:\\test\\admin-panel\\src\\pages\\Users.tsx": "6", "D:\\test\\admin-panel\\src\\pages\\Posts.tsx": "7", "D:\\test\\admin-panel\\src\\pages\\Comments.tsx": "8", "D:\\test\\admin-panel\\src\\pages\\Settings.tsx": "9", "D:\\test\\admin-panel\\src\\components\\auth\\ProtectedRoute.tsx": "10", "D:\\test\\admin-panel\\src\\components\\layout\\Layout.tsx": "11", "D:\\test\\admin-panel\\src\\components\\auth\\LoginForm.tsx": "12", "D:\\test\\admin-panel\\src\\services\\apiService.ts": "13", "D:\\test\\admin-panel\\src\\services\\authService.ts": "14", "D:\\test\\admin-panel\\src\\components\\layout\\Sidebar.tsx": "15", "D:\\test\\admin-panel\\src\\components\\layout\\Header.tsx": "16", "D:\\test\\admin-panel\\src\\components\\dashboard\\StatsCard.tsx": "17", "D:\\test\\admin-panel\\src\\components\\dashboard\\RecentActivity.tsx": "18", "D:\\test\\admin-panel\\src\\config\\api.ts": "19"}, {"size": 554, "mtime": 1756116388942, "results": "20", "hashOfConfig": "21"}, {"size": 425, "mtime": 1756116388095, "results": "22", "hashOfConfig": "21"}, {"size": 2804, "mtime": 1756116950499, "results": "23", "hashOfConfig": "21"}, {"size": 5716, "mtime": 1756116804198, "results": "24", "hashOfConfig": "21"}, {"size": 2211, "mtime": 1756116653173, "results": "25", "hashOfConfig": "21"}, {"size": 10673, "mtime": 1756116844435, "results": "26", "hashOfConfig": "21"}, {"size": 4409, "mtime": 1756116865497, "results": "27", "hashOfConfig": "21"}, {"size": 4205, "mtime": 1756116935466, "results": "28", "hashOfConfig": "21"}, {"size": 11955, "mtime": 1756116900829, "results": "29", "hashOfConfig": "21"}, {"size": 1514, "mtime": 1756116917634, "results": "30", "hashOfConfig": "21"}, {"size": 917, "mtime": 1756116716259, "results": "31", "hashOfConfig": "21"}, {"size": 4946, "mtime": 1756116669766, "results": "32", "hashOfConfig": "21"}, {"size": 6759, "mtime": 1756116746469, "results": "33", "hashOfConfig": "21"}, {"size": 4094, "mtime": 1756116642054, "results": "34", "hashOfConfig": "21"}, {"size": 4545, "mtime": 1756116696239, "results": "35", "hashOfConfig": "21"}, {"size": 2251, "mtime": 1756116708724, "results": "36", "hashOfConfig": "21"}, {"size": 1506, "mtime": 1756116765112, "results": "37", "hashOfConfig": "21"}, {"size": 3570, "mtime": 1756116780525, "results": "38", "hashOfConfig": "21"}, {"size": 2033, "mtime": 1756116625069, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "12gre67", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\test\\admin-panel\\src\\index.tsx", [], [], "D:\\test\\admin-panel\\src\\reportWebVitals.ts", [], [], "D:\\test\\admin-panel\\src\\App.tsx", [], [], "D:\\test\\admin-panel\\src\\pages\\Dashboard.tsx", [], [], "D:\\test\\admin-panel\\src\\contexts\\AuthContext.tsx", [], [], "D:\\test\\admin-panel\\src\\pages\\Users.tsx", ["97"], [], "D:\\test\\admin-panel\\src\\pages\\Posts.tsx", [], [], "D:\\test\\admin-panel\\src\\pages\\Comments.tsx", [], [], "D:\\test\\admin-panel\\src\\pages\\Settings.tsx", [], [], "D:\\test\\admin-panel\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "D:\\test\\admin-panel\\src\\components\\layout\\Layout.tsx", [], [], "D:\\test\\admin-panel\\src\\components\\auth\\LoginForm.tsx", [], [], "D:\\test\\admin-panel\\src\\services\\apiService.ts", [], [], "D:\\test\\admin-panel\\src\\services\\authService.ts", [], [], "D:\\test\\admin-panel\\src\\components\\layout\\Sidebar.tsx", [], [], "D:\\test\\admin-panel\\src\\components\\layout\\Header.tsx", ["98"], [], "D:\\test\\admin-panel\\src\\components\\dashboard\\StatsCard.tsx", [], [], "D:\\test\\admin-panel\\src\\components\\dashboard\\RecentActivity.tsx", [], [], "D:\\test\\admin-panel\\src\\config\\api.ts", [], [], {"ruleId": "99", "severity": 1, "message": "100", "line": 29, "column": 6, "nodeType": "101", "endLine": 29, "endColumn": 19, "suggestions": "102"}, {"ruleId": "103", "severity": 1, "message": "104", "line": 2, "column": 30, "nodeType": "105", "messageId": "106", "endLine": 2, "endColumn": 34}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", "ArrayExpression", ["107"], "@typescript-eslint/no-unused-vars", "'User' is defined but never used.", "Identifier", "unusedVar", {"desc": "108", "fix": "109"}, "Update the dependencies array to be: [currentPage, fetchUsers]", {"range": "110", "text": "111"}, [718, 731], "[currentPage, fetchUsers]"]