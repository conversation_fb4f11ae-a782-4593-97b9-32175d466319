{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 10V9\",\n  key: \"1s66rd\"\n}], [\"path\", {\n  d: \"M16 15v-1\",\n  key: \"kmf0wu\"\n}], [\"path\", {\n  d: \"M16 21v-2\",\n  key: \"63oau2\"\n}], [\"path\", {\n  d: \"M16 5V3\",\n  key: \"2dizs0\"\n}], [\"path\", {\n  d: \"M8 10V9\",\n  key: \"fun8gx\"\n}], [\"path\", {\n  d: \"M8 15v-1\",\n  key: \"fel0bl\"\n}], [\"path\", {\n  d: \"M8 21v-2\",\n  key: \"1lp7tu\"\n}], [\"path\", {\n  d: \"M8 5V3\",\n  key: \"19j4ll\"\n}], [\"rect\", {\n  x: \"3\",\n  y: \"3\",\n  width: \"18\",\n  height: \"18\",\n  rx: \"2\",\n  key: \"h1oib\"\n}]];\nconst PanelLeftRightDashed = createLucideIcon(\"panel-left-right-dashed\", __iconNode);\nexport { __iconNode, PanelLeftRightDashed as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x", "y", "width", "height", "rx", "PanelLeftRightDashed", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\panel-left-right-dashed.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 10V9', key: '1s66rd' }],\n  ['path', { d: 'M16 15v-1', key: 'kmf0wu' }],\n  ['path', { d: 'M16 21v-2', key: '63oau2' }],\n  ['path', { d: 'M16 5V3', key: '2dizs0' }],\n  ['path', { d: 'M8 10V9', key: 'fun8gx' }],\n  ['path', { d: 'M8 15v-1', key: 'fel0bl' }],\n  ['path', { d: 'M8 21v-2', key: '1lp7tu' }],\n  ['path', { d: 'M8 5V3', key: '19j4ll' }],\n  ['rect', { x: '3', y: '3', width: '18', height: '18', rx: '2', key: 'h1oib' }],\n];\n\n/**\n * @component @name PanelLeftRightDashed\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMTBWOSIgLz4KICA8cGF0aCBkPSJNMTYgMTV2LTEiIC8+CiAgPHBhdGggZD0iTTE2IDIxdi0yIiAvPgogIDxwYXRoIGQ9Ik0xNiA1VjMiIC8+CiAgPHBhdGggZD0iTTggMTBWOSIgLz4KICA8cGF0aCBkPSJNOCAxNXYtMSIgLz4KICA8cGF0aCBkPSJNOCAyMXYtMiIgLz4KICA8cGF0aCBkPSJNOCA1VjMiIC8+CiAgPHJlY3QgeD0iMyIgeT0iMyIgd2lkdGg9IjE4IiBoZWlnaHQ9IjE4IiByeD0iMiIgLz4KPC9zdmc+) - https://lucide.dev/icons/panel-left-right-dashed\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PanelLeftRightDashed = createLucideIcon('panel-left-right-dashed', __iconNode);\n\nexport default PanelLeftRightDashed;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAUC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAKC,CAAA,EAAG;EAAKC,KAAA,EAAO;EAAMC,MAAA,EAAQ;EAAMC,EAAA,EAAI;EAAKL,GAAA,EAAK;AAAA,CAAS,EAC/E;AAaA,MAAMM,oBAAA,GAAuBC,gBAAA,CAAiB,2BAA2BT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}