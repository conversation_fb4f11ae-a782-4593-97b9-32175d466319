{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"21\",\n  x2: \"14\",\n  y1: \"4\",\n  y2: \"4\",\n  key: \"obuewd\"\n}], [\"line\", {\n  x1: \"10\",\n  x2: \"3\",\n  y1: \"4\",\n  y2: \"4\",\n  key: \"1q6298\"\n}], [\"line\", {\n  x1: \"21\",\n  x2: \"12\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"1iu8h1\"\n}], [\"line\", {\n  x1: \"8\",\n  x2: \"3\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"ntss68\"\n}], [\"line\", {\n  x1: \"21\",\n  x2: \"16\",\n  y1: \"20\",\n  y2: \"20\",\n  key: \"14d8ph\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"3\",\n  y1: \"20\",\n  y2: \"20\",\n  key: \"m0wm8r\"\n}], [\"line\", {\n  x1: \"14\",\n  x2: \"14\",\n  y1: \"2\",\n  y2: \"6\",\n  key: \"14e1ph\"\n}], [\"line\", {\n  x1: \"8\",\n  x2: \"8\",\n  y1: \"10\",\n  y2: \"14\",\n  key: \"1i6ji0\"\n}], [\"line\", {\n  x1: \"16\",\n  x2: \"16\",\n  y1: \"18\",\n  y2: \"22\",\n  key: \"1lctlv\"\n}]];\nconst SlidersHorizontal = createLucideIcon(\"sliders-horizontal\", __iconNode);\nexport { __iconNode, SlidersHorizontal as default };", "map": {"version": 3, "names": ["__iconNode", "x1", "x2", "y1", "y2", "key", "SlidersHorizontal", "createLucideIcon"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\icons\\sliders-horizontal.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '21', x2: '14', y1: '4', y2: '4', key: 'obuewd' }],\n  ['line', { x1: '10', x2: '3', y1: '4', y2: '4', key: '1q6298' }],\n  ['line', { x1: '21', x2: '12', y1: '12', y2: '12', key: '1iu8h1' }],\n  ['line', { x1: '8', x2: '3', y1: '12', y2: '12', key: 'ntss68' }],\n  ['line', { x1: '21', x2: '16', y1: '20', y2: '20', key: '14d8ph' }],\n  ['line', { x1: '12', x2: '3', y1: '20', y2: '20', key: 'm0wm8r' }],\n  ['line', { x1: '14', x2: '14', y1: '2', y2: '6', key: '14e1ph' }],\n  ['line', { x1: '8', x2: '8', y1: '10', y2: '14', key: '1i6ji0' }],\n  ['line', { x1: '16', x2: '16', y1: '18', y2: '22', key: '1lctlv' }],\n];\n\n/**\n * @component @name SlidersHorizontal\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMjEiIHgyPSIxNCIgeTE9IjQiIHkyPSI0IiAvPgogIDxsaW5lIHgxPSIxMCIgeDI9IjMiIHkxPSI0IiB5Mj0iNCIgLz4KICA8bGluZSB4MT0iMjEiIHgyPSIxMiIgeTE9IjEyIiB5Mj0iMTIiIC8+CiAgPGxpbmUgeDE9IjgiIHgyPSIzIiB5MT0iMTIiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMjEiIHgyPSIxNiIgeTE9IjIwIiB5Mj0iMjAiIC8+CiAgPGxpbmUgeDE9IjEyIiB4Mj0iMyIgeTE9IjIwIiB5Mj0iMjAiIC8+CiAgPGxpbmUgeDE9IjE0IiB4Mj0iMTQiIHkxPSIyIiB5Mj0iNiIgLz4KICA8bGluZSB4MT0iOCIgeDI9IjgiIHkxPSIxMCIgeTI9IjE0IiAvPgogIDxsaW5lIHgxPSIxNiIgeDI9IjE2IiB5MT0iMTgiIHkyPSIyMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/sliders-horizontal\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SlidersHorizontal = createLucideIcon('sliders-horizontal', __iconNode);\n\nexport default SlidersHorizontal;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAKC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEJ,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAKC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,QAAQ;EAAEJ,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,QAAQ;EAAEJ,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEJ,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,QAAQ;EAAEJ,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEJ,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAKC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEJ,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEJ,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,GAAA,EAAK;AAAA,CAAU,EACpE;AAaA,MAAMC,iBAAA,GAAoBC,gBAAA,CAAiB,sBAAsBP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}