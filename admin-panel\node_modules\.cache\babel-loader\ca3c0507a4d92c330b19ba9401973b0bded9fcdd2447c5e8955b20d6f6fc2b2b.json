{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.js';\nimport { mergeClasses, hasA11yProp } from './shared/src/utils.js';\nconst Icon = forwardRef(({\n  color = \"currentColor\",\n  size = 24,\n  strokeWidth = 2,\n  absoluteStrokeWidth,\n  className = \"\",\n  children,\n  iconNode,\n  ...rest\n}, ref) => createElement(\"svg\", {\n  ref,\n  ...defaultAttributes,\n  width: size,\n  height: size,\n  stroke: color,\n  strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n  className: mergeClasses(\"lucide\", className),\n  ...(!children && !hasA11yProp(rest) && {\n    \"aria-hidden\": \"true\"\n  }),\n  ...rest\n}, [...iconNode.map(([tag, attrs]) => createElement(tag, attrs)), ...(Array.isArray(children) ? children : [children])]));\nexport { Icon as default };", "map": {"version": 3, "names": ["Icon", "forwardRef", "color", "size", "strokeWidth", "absoluteStrokeWidth", "className", "children", "iconNode", "rest", "ref", "createElement", "defaultAttributes", "width", "height", "stroke", "Number", "mergeClasses", "hasA11yProp", "map", "tag", "attrs", "Array", "isArray"], "sources": ["D:\\test\\admin-panel\\node_modules\\lucide-react\\src\\Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "mappings": ";;;;;;;;;;AAwBA,MAAMA,IAAA,GAAOC,UAAA,CACX,CACE;EACEC,KAAA,GAAQ;EACRC,IAAA,GAAO;EACPC,WAAA,GAAc;EACdC,mBAAA;EACAC,SAAA,GAAY;EACZC,QAAA;EACAC,QAAA;EACA,GAAGC;AAAA,GAELC,GAAA,KAEAC,aAAA,CACE,OACA;EACED,GAAA;EACA,GAAGE,iBAAA;EACHC,KAAA,EAAOV,IAAA;EACPW,MAAA,EAAQX,IAAA;EACRY,MAAA,EAAQb,KAAA;EACRE,WAAA,EAAaC,mBAAA,GAAuBW,MAAA,CAAOZ,WAAW,IAAI,KAAMY,MAAA,CAAOb,IAAI,IAAIC,WAAA;EAC/EE,SAAA,EAAWW,YAAA,CAAa,UAAUX,SAAS;EAC3C,IAAI,CAACC,QAAA,IAAY,CAACW,WAAA,CAAYT,IAAI,KAAK;IAAE,eAAe;EAAA,CAAO;EAC/D,GAAGA;AAAA,CACL,EACA,CACE,GAAGD,QAAA,CAASW,GAAA,CAAI,CAAC,CAACC,GAAA,EAAKC,KAAK,MAAMV,aAAA,CAAcS,GAAA,EAAKC,KAAK,CAAC,GAC3D,IAAIC,KAAA,CAAMC,OAAA,CAAQhB,QAAQ,IAAIA,QAAA,GAAW,CAACA,QAAQ,GACpD,CAEN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}